import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/core/widgets/user_profile_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/tap_user_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/gift_modal.dart';
import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_type.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

class VoiceCallWidget extends ConsumerStatefulWidget {
  // final AutoDisposeNotifierProvider<VoiceCallBaseMixin, VoiceCallState>
  //     provider;
  final String nickName;
  final String userId;
  final String? avatar;

  final bool isAudioEncryptEnabled;
  final bool isAudioDecryptEnabled;

  final CallStatus callStatus;
  final Duration duration;

  final Future<VoidResult> Function(
      GiftModel gift, List<String> selectedUserIds) sendGiftMessage;
  final Future<VoidResult> Function(BagGiftModel gift, String recipientUserId)
      sendBagGiftMessage;
  final Future<void> Function(bool enabled) toggleAudioEncryption;
  final Future<void> Function(bool enabled) toggleAudioDecryption;
  final Future<void> Function() acceptCall;
  final Future<void> Function() rejectCall;
  final Future<void> Function() endCall;
  final Future<void> Function(bool muted) toggleMicrophone;
  final Future<void> Function(bool enabled) toggleSpeaker;
  final Future<List<MediaDeviceInfo>> Function() getAudioInputDevices;
  final Future<void> Function(String deviceId) selectAudioInputDevice;
  final Future<void> Function() startRingtone;
  final Future<void> Function() stopRingtone;

  const VoiceCallWidget({
    super.key,
    required this.nickName,
    required this.userId,
    required this.callStatus,
    required this.duration,
    required this.isAudioEncryptEnabled,
    required this.isAudioDecryptEnabled,
    required this.toggleAudioEncryption,
    required this.toggleAudioDecryption,
    required this.acceptCall,
    required this.rejectCall,
    required this.endCall,
    required this.toggleMicrophone,
    required this.toggleSpeaker,
    required this.sendGiftMessage,
    required this.sendBagGiftMessage,
    required this.getAudioInputDevices,
    required this.selectAudioInputDevice,
    required this.startRingtone,
    required this.stopRingtone,
    this.avatar,
  });

  @override
  ConsumerState<VoiceCallWidget> createState() => _VoiceCallWidgetState();
}

class _VoiceCallWidgetState extends ConsumerState<VoiceCallWidget>
    with TapUserMixin {
  bool _isMuted = false;
  bool _isSpeakerOn = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.startRingtone();
    });
  }

  @override
  void dispose() {
    widget.stopRingtone();
    super.dispose();
  }

  Future<void> _onSend(GiftModel gift, List<String> selectedUserIds) async {
    context.pop();

    final result = await widget.sendGiftMessage(gift, selectedUserIds);

    result.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) async {
        if (!mounted) return;
        const dialogTag = 'voice_call_gift_effect';
        await context.showFullscreenGiftEffect(
          svgaUrl: gift.svgaUrl,
          imageUrl: gift.imageUrl,
          dialogTag: dialogTag,
          giftType: gift.giftType ?? GiftType.gift,
        );
      },
    );
  }

  Future<void> _onSendBag(BagGiftModel gift, String recipientUserId) async {
    context.pop();
    final result = await widget.sendBagGiftMessage(gift, recipientUserId);

    result.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) async {
        if (!mounted) return;
        const dialogTag = 'voice_call_gift_effect';
        await context.showFullscreenGiftEffect(
          svgaUrl: gift.svgaUrl,
          imageUrl: gift.imageUrl,
          dialogTag: dialogTag,
          giftType: gift.giftType ?? GiftType.gift,
        );
      },
    );
  }

  void _showGiftModal({
    required String peerId,
    String? peerNickName,
    String? peerAvatar,
    int initialTabIndex = 0,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => GiftModal(
        onSend: _onSend,
        onSendBag: _onSendBag,
        initialTabIndex: initialTabIndex,
        selectedUser: ProfileModel(
          id: peerId,
          nickName: peerNickName,
          avatar: peerAvatar,
        ),
        isAudioRoom: true,
      ),
    );
  }

  void _showUserProfileModal() {
    showAdaptiveDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => UserProfileModal(
        userId: widget.userId,
        nickName: widget.nickName,
        avatar: widget.avatar,
        handleReportAction: () async {
          context.maybePop();
          handleReportUserAction(widget.userId);
        },
        showGiftModal: () async {
          context.pop();
          _showGiftModal(
            peerId: widget.userId,
            peerNickName: widget.nickName,
            peerAvatar: widget.avatar,
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: widget.callStatus is CallDisconnected,
      child: buildBody(),
    );
  }

  Widget buildBody() {
    return SizedBox.expand(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          50.verticalSpace,
          _userInfos(),
          const Spacer(),
          _buildDuration(),
          30.verticalSpace,
          _buildControlButtons(),
          88.verticalSpace,
          if (widget.callStatus is CallConnected) ...[
            _buildGiftButtons(),
            60.verticalSpace,
          ] else
            const Spacer(),
          SizedBox(
            height: MediaQuery.of(context).padding.bottom,
          ),
        ],
      ),
    );
  }

  Widget _buildDuration() {
    return Opacity(
      opacity: widget.callStatus is CallConnected ? 1 : 0,
      child: Builder(builder: (_) {
        final duration = widget.duration;
        final hours = duration.inHours;
        final minutes = duration.inMinutes.remainder(60);
        final seconds = duration.inSeconds.remainder(60);
        final formattedDuration = hours > 0
            ? '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}'
            : '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        return Text(
          formattedDuration,
          style: Theme.of(context).textTheme.titleLarge,
        );
      }),
    );
  }

  Widget _userInfos() {
    return GestureDetector(
      onTap: () {
        if (widget.callStatus is CallDisconnected) return;
        _showUserProfileModal();
      },
      child: Column(
        children: [
          AvatarWithFrame(
            avatarUrl: widget.avatar ?? '',
            width: 125.w,
            height: 125.w,
          ),
          Text(
            widget.nickName,
            style: context.textTheme.titleLarge,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    if (widget.callStatus is CallIncoming) {
      // 被呼叫方的按钮
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          AppButton(
            type: AppButtonType.contained,
            borderRadius: 999,
            backgroundColor: Colors.green,
            width: 50.w,
            height: 50.w,
            onPressed: () {
              widget.acceptCall();
            },
            child: const Icon(
              Icons.call,
              color: Colors.white,
            ),
          ),
          AppButton(
            type: AppButtonType.contained,
            borderRadius: 999,
            backgroundColor: Colors.red,
            width: 50.w,
            height: 50.w,
            onPressed: () {
              widget.rejectCall();
            },
            child: const Icon(
              Icons.call_end,
              color: Colors.white,
            ),
          ),
        ],
      );
    } else if (widget.callStatus is WaitingForAnswer) {
      // 呼叫方的按钮
      return AppButton(
        type: AppButtonType.contained,
        borderRadius: 999,
        backgroundColor: Colors.red,
        width: 50.w,
        height: 50.w,
        onPressed: () {
          widget.endCall();
        },
        child: const Icon(
          Icons.call_end,
          color: Colors.white,
        ),
      );
    } else if (widget.callStatus is CallConnected) {
      // 通话中的按钮
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          AppButton(
            type: AppButtonType.contained,
            borderRadius: 999,
            width: 50.w,
            height: 50.w,
            onPressed: () {
              setState(() => _isMuted = !_isMuted);
              widget.toggleMicrophone(_isMuted);
            },
            child: Icon(
              _isMuted ? Icons.mic_off : Icons.mic,
            ),
          ),
          AppButton(
            type: AppButtonType.contained,
            borderRadius: 999,
            backgroundColor: Colors.red,
            width: 50.w,
            height: 50.w,
            onPressed: () {
              widget.endCall();
            },
            child: const Icon(
              Icons.call_end,
              color: Colors.white,
            ),
          ),
          AppButton(
            type: AppButtonType.contained,
            borderRadius: 999,
            width: 50.w,
            height: 50.w,
            onPressed: () {
              setState(() => _isSpeakerOn = !_isSpeakerOn);
              widget.toggleSpeaker(_isSpeakerOn);
            },
            child: Icon(
              _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
            ),
          ),
        ],
      );
    } else if (widget.callStatus is CallDisconnected) {
      return Column(children: [
        _buildDuration(),
        10.verticalSpace,
        AppButton(
          onPressed: () {},
          type: AppButtonType.contained,
          child: Icon(
            Icons.call_end,
            color: Colors.red,
            size: 32.w,
          ),
        )
      ]);
    } else {
      // 其他状态只显示挂断按钮
      return AppButton(
        type: AppButtonType.contained,
        onPressed: () {
          if (widget.callStatus is CallDisconnected) {
            return;
          }
          widget.endCall();
        },
        child: Icon(
          Icons.call_end,
          color: Colors.red,
          size: 32.w,
        ),
      );
    }
  }

  Widget _buildGiftButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AppButton(
          type: AppButtonType.contained,
          onPressed: () {
            _showGiftModal(
              peerId: widget.userId,
              peerNickName: widget.nickName,
              peerAvatar: widget.avatar,
            );
          },
          child: Assets.images.giftStore.image(),
        ),
        50.horizontalSpace,
        AppButton(
          type: AppButtonType.contained,
          onPressed: () {
            _showGiftModal(
              peerId: widget.userId,
              peerNickName: widget.nickName,
              peerAvatar: widget.avatar,
              initialTabIndex: 1,
            );
          },
          child: Assets.images.gift.image(),
        ),
      ],
    );
  }
}
