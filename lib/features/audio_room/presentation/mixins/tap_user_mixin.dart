import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/report_reason_screen.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/report_user_screen.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/gift_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/user_action_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/user_select_modal.dart';
import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_scene.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_type.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

mixin TapUserMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  Future<void> _handleMicAction(RoomUser user, bool isOnMic) async {
    if (user.uid == null) {
      LoadingUtils.showError('User not found');
      return;
    }

    final uid = user.uid ?? -1;

    final res = isOnMic
        ? await ref.read(audioRoomProvider.notifier).dropMic(uid)
        : await ref.read(audioRoomProvider.notifier).inviteMic(uid);

    res.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) => context.maybePop(),
    );
  }

  Future<void> _handleMuteAction(RoomUser user, bool isMuted) async {
    final uid = user.uid ?? -1;
    final targetUserIsSelf = ref.read(
        audioRoomProvider.select((state) => state.currentUser?.uid == uid));
    final result = targetUserIsSelf
        ? await ref.read(audioRoomProvider.notifier).toggleMuteByLocal()
        : await ref.read(audioRoomProvider.notifier).toggleMuteByRemote(
              uid,
              mute: !isMuted,
            );

    result.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) => context.maybePop(),
    );
  }

  Future<void> _handleManagerAction(RoomUser user, bool isManager) async {
    final uid = user.uid ?? -1;
    final result = isManager
        ? await ref.read(audioRoomProvider.notifier).removeManager(
              targetUid: uid,
              targetUserId: user.userId ?? '',
            )
        : await ref.read(audioRoomProvider.notifier).inviteManager(uid);

    result.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) => context.maybePop(),
    );
  }

  Future<void> _handleKickAction(RoomUser user) async {
    final uid = user.uid ?? -1;
    final res = await ref.read(audioRoomProvider.notifier).kickUser(
          uid,
          user.userId ?? '',
        );
    res.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) => context.maybePop(),
    );
  }

  Future<void> handleGiftAction(
    GiftModel gift,
    List<String> selectedUserIds,
    bool isAudioRoom,
  ) async {
    context.maybePop();

    VoidResult res;
    if (isAudioRoom) {
      if (gift.giftType == GiftType.frame) {
        res = await ref.read(audioRoomProvider.notifier).sendFrame(
              frame: gift,
              selectedUserIds: selectedUserIds,
            );
      } else {
        res = await ref.read(audioRoomProvider.notifier).sendGift(
              gift: gift,
              selectedUserIds: selectedUserIds,
            );
      }
    } else {
      const scene = GiftScene.profile;
      if (gift.giftType == GiftType.frame) {
        final result =
            await ref.read(giftStateNotifierProvider.notifier).sendFrame(
                  recipientUserIds: selectedUserIds,
                  frameId: gift.id ?? 0,
                  scene: scene,
                );
        res = result.fold(
          (l) => Left(l),
          (r) => const Right(null),
        );
      } else {
        final result =
            await ref.read(giftStateNotifierProvider.notifier).sendGift(
                  recipientUserIds: selectedUserIds,
                  giftId: gift.id ?? 0,
                  scene: scene,
                );
        res = result.fold(
          (l) => Left(l),
          (r) => const Right(null),
        );
      }
    }

    // 发送礼物成功后更新钱包余额
    res.fold(
      (error) => LoadingUtils.showError(error.message),
      (success) async {
        // 更新钱包余额
        await ref.read(accountProvider.notifier).getUserWallet();
      },
    );

    if (!mounted) return;

    res.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) {
        context.showFullscreenGiftEffect(
          svgaUrl: gift.svgaUrl,
          imageUrl: gift.imageUrl,
          dialogTag: 'gift_message_dialog',
          giftType: gift.giftType ?? GiftType.gift,
        );
      },
    );
  }

  Future<void> handleBagGiftAction(
    BagGiftModel bagGift,
    String recipientUserId,
    bool isAudioRoom,
  ) async {
    context.maybePop();

    final currentUser = isAudioRoom
        ? ref.read(audioRoomProvider.select((state) => state.currentUser))
        : null;

    if (currentUser != null &&
        currentUser.userId == bagGift.userId &&
        recipientUserId == currentUser.userId &&
        bagGift.giftType == GiftType.frame) {
      return handleActiveFrame(bagGift);
    }

    VoidResult res;
    if (isAudioRoom) {
      res = await ref.read(audioRoomProvider.notifier).sendBagGift(
            bagGift: bagGift,
            recipientUserId: recipientUserId,
          );
    } else {
      const scene = GiftScene.profile;
      final result =
          await ref.read(giftStateNotifierProvider.notifier).sendBagGift(
                recipientUserId: recipientUserId,
                bagGiftId: bagGift.id ?? 0,
                scene: scene,
              );
      res = result.fold(
        (l) => Left(l),
        (r) => const Right(null),
      );
    }

    res.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) async {
        // 发送背包礼物成功后更新钱包余额
        await ref.read(accountProvider.notifier).getUserWallet();

        if (mounted) {
          context.showFullscreenGiftEffect(
            svgaUrl: bagGift.svgaUrl,
            imageUrl: bagGift.imageUrl,
            dialogTag: 'gift_message_dialog',
            giftType: bagGift.giftType ?? GiftType.gift,
          );
        }
      },
    );
  }

  Future<void> toReportUserPage(ReportSource source) async {
    context.push(
      WidgetPageConfig(
        page: ReportUserScreen(
          source: source,
        ),
      ),
    );
  }

  Future<void> toReportRoomPage(ReportSource source) async {
    context.push(
      WidgetPageConfig(
        page: ReportReasonScreen(
          source: source,
          reportType: 'Other',
          title: 'Report Issue',
          description: 'Please briefly describe your report',
        ),
      ),
    );
  }

  Future<void> _showUserSelectSheet() async {
    final userId = await context.showCupertinoModalBottomSheet<String>(
      builder: (context) => const UserSelectModal(),
    );
    if (userId != null) {
      toReportUserPage(UserReportSource(userId: userId));
    }
  }

  Future<void> handleReportAction() async {
    _showUserSelectSheet();
  }

  Future handleReportUserAction(String userId) async {
    toReportUserPage(UserReportSource(userId: userId));
  }

  Future<void> handleActiveFrame(BagGiftModel bagGift) async {
    final res = await ref.read(audioRoomProvider.notifier).activeFrame(
          bagGift,
        );

    res.fold(
      (l) => LoadingUtils.showError(l.message),
      (r) => context.showFullscreenGiftEffect(
        svgaUrl: bagGift.svgaUrl,
        imageUrl: bagGift.imageUrl,
        dialogTag: 'gift_message_dialog',
        giftType: bagGift.giftType ?? GiftType.gift,
      ),
    );
  }

  Future<void> showGiftModal(
    ProfileModel? user, {
    required bool isAudioRoom,
  }) async {
    if (mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => GiftModal(
          onSend: (gift, selectedUserIds) => handleGiftAction(
            gift,
            selectedUserIds,
            isAudioRoom,
          ),
          onSendBag: (bagGift, recipientUserId) => handleBagGiftAction(
            bagGift,
            recipientUserId,
            isAudioRoom,
          ),
          selectedUser: user,
          isAudioRoom: isAudioRoom,
        ),
      );
    }
  }

  Future<void> handleTapUser(RoomUser user) async {
    return showAdaptiveDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => UserActionModal(
        user: user,
        handleMicAction: _handleMicAction,
        handleMuteAction: _handleMuteAction,
        handleManagerAction: _handleManagerAction,
        handleKickAction: _handleKickAction,
        handleReportAction: (user) async {
          context.maybePop();
          await handleReportUserAction(user.userId ?? '');
        },
        handleFollowAction: () async {
          ref.read(audioRoomProvider.notifier).sendFollowMessage(user);
        },
        showGiftModal: (user) async {
          context.maybePop();
          await Future.delayed(const Duration(milliseconds: 300));
          showGiftModal(user.userInfo.profile, isAudioRoom: false);
        },
      ),
    );
  }
}
