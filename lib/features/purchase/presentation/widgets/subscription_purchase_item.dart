import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../domain/entities/purchase_item.dart';

class SubscriptionPurchaseItem extends StatelessWidget {
  const SubscriptionPurchaseItem({
    super.key,
    required this.item,
    required this.onPurchase,
    this.buttonText = 'Subscribe',
    this.isDisabled = false,
    this.isPopular = false,
    this.isBestValue = false,
    this.primaryColor,
    this.primaryLightColor,
    this.onPrimaryColor,
  });

  final PurchaseItem item;
  final VoidCallback onPurchase;
  final String buttonText;
  final bool isDisabled;
  final bool isPopular;
  final bool isBestValue;
  final Color? primaryColor;
  final Color? primaryLightColor;
  final Color? onPrimaryColor;

  @override
  Widget build(BuildContext context) {
    final hasDiscount = item.product.discountRate > 0;
    final effectivePrimaryColor = primaryColor ?? Colors.purple.shade400;
    final effectivePrimaryLightColor =
        primaryLightColor ?? Colors.purple.shade200;
    final effectiveOnPrimaryColor = onPrimaryColor ?? Colors.white;

    return Container(
      padding: EdgeInsets.all(16.r),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部订阅类型和时长
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      item.product.description,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  if (hasDiscount)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: effectivePrimaryColor.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Text(
                        'Save ${item.product.discountRate}%',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: effectivePrimaryLightColor,
                        ),
                      ),
                    ),
                ],
              ),

              16.verticalSpace,

              // 价格部分
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    item.product.currencySymbol,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    '${hasDiscount ? item.product.discountPrice : item.product.price}',
                    style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  12.horizontalSpace,
                  if (hasDiscount)
                    Text(
                      '${item.product.currencySymbol}${item.product.price}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        decoration: TextDecoration.lineThrough,
                        color: Colors.white.withValues(alpha: 0.5),
                      ),
                    ),
                ],
              ),

              16.verticalSpace,

              // 订阅按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: isDisabled ? null : onPurchase,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isDisabled
                        ? Colors.grey.shade800
                        : effectivePrimaryColor,
                    foregroundColor: effectiveOnPrimaryColor,
                    elevation: 0,
                    padding: EdgeInsets.symmetric(vertical: 14.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    disabledBackgroundColor: Colors.grey.shade800,
                    disabledForegroundColor:
                        Colors.white.withValues(alpha: 0.6),
                  ),
                  child: Text(
                    buttonText,
                    style: TextStyle(
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              12.verticalSpace,

              // 订阅说明
              Text(
                'Subscription will automatically renew unless canceled at least 24 hours before the end of the current period.',
                style: TextStyle(
                  fontSize: 11.sp,
                  color: Colors.white.withValues(alpha: 0.5),
                  height: 1.4,
                ),
              ),
            ],
          ),

          // 标签
          if (isPopular)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.pink.shade400,
                      effectivePrimaryColor,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  'Popular',
                  style: TextStyle(
                    color: effectiveOnPrimaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ),
          if (isBestValue)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.shade400,
                      effectivePrimaryColor,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  'Best Value',
                  style: TextStyle(
                    color: effectiveOnPrimaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
