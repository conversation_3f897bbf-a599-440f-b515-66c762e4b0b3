import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/purchase_process_state_provider.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/subscription_purchase_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../widgets/subscription_purchase_item.dart';

class SubscriptionPurchasePage extends ConsumerWidget {
  const SubscriptionPurchasePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final purchaseState = ref.watch(subscriptionPurchaseProvider);
    final processState = ref.watch(purchaseProcessStateProvider);
    final primaryColor = Theme.of(context).colorScheme.primary;
    final onPrimaryColor = Theme.of(context).colorScheme.onPrimary;
    final primaryLightColor =
        HSLColor.fromColor(primaryColor).withLightness(0.6).toColor();

    // 监听购买状态变化
    ref.listen(purchaseProcessStateProvider, (previous, current) {
      if (previous != current && current == PurchaseProcessState.verified) {
        // 订阅验证成功，可以在这里执行额外操作
        LoadingUtils.showToast('Subscription successful!');
      } else if (previous != current &&
          current == PurchaseProcessState.failed) {
        // 订阅失败，可以显示重试按钮或其他UI
      } else if (previous != current &&
          current == PurchaseProcessState.canceled) {
        LoadingUtils.showToast('Subscription cancelled');
      }
    });

    // 判断按钮是否应该禁用
    bool isPurchasing() {
      return processState == PurchaseProcessState.purchasing ||
          processState == PurchaseProcessState.verifying;
    }

    return PopScope(
      canPop: !isPurchasing(),
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          elevation: 0,
          centerTitle: true,
          title: Text(
            'Premium Subscription',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 20.sp,
            ),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: Stack(
          children: [
            CustomScrollView(
              slivers: [
                // 顶部介绍卡片
                SliverToBoxAdapter(
                  child: Container(
                    margin: EdgeInsets.all(20.r),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          primaryColor.withValues(alpha: 0.8),
                          primaryLightColor.withValues(alpha: 0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    padding: EdgeInsets.all(20.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(10.r),
                              decoration: BoxDecoration(
                                color: onPrimaryColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Icon(
                                Icons.verified,
                                color: onPrimaryColor,
                                size: 24.r,
                              ),
                            ),
                            16.horizontalSpace,
                            Expanded(
                              child: Text(
                                'Unlock Premium Features',
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                  color: onPrimaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        20.verticalSpace,
                        _buildFeatureRow(
                            context, 'Unlimited audio rooms', onPrimaryColor),
                        12.verticalSpace,
                        _buildFeatureRow(
                            context, 'Higher quality audio', onPrimaryColor),
                        12.verticalSpace,
                        _buildFeatureRow(
                            context, 'No advertisements', onPrimaryColor),
                        12.verticalSpace,
                        _buildFeatureRow(context, 'Priority customer support',
                            onPrimaryColor),
                      ],
                    ),
                  ),
                ),

                // 订阅计划列表
                purchaseState.when(
                  data: (items) {
                    if (items.isEmpty) {
                      return const SliverFillRemaining(
                        child: Center(
                          child: Text(
                            'No subscription plans available',
                            style: TextStyle(color: Colors.white70),
                          ),
                        ),
                      );
                    }

                    return SliverPadding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      sliver: SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final item = items[index];
                            final isPopular = item.product.isPopular == 1;
                            final isBestValue = item.product.bestValue == 1;

                            return Container(
                              margin: EdgeInsets.only(bottom: 16.h),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.05),
                                borderRadius: BorderRadius.circular(16.r),
                                border: Border.all(
                                  color: isPopular || isBestValue
                                      ? primaryColor
                                      : Colors.transparent,
                                  width: 1.5,
                                ),
                              ),
                              child: SubscriptionPurchaseItem(
                                item: item,
                                isPopular: isPopular,
                                isBestValue: isBestValue,
                                onPurchase: () {
                                  // 如果正在处理购买，禁止点击
                                  if (isPurchasing()) {
                                    LoadingUtils.showToast(
                                        'Processing, please wait');
                                    return;
                                  }
                                  ref
                                      .read(
                                          subscriptionPurchaseProvider.notifier)
                                      .purchaseSubscription(
                                        item.productId,
                                      );
                                },
                                // 只有当前选中的产品才会显示特殊按钮状态
                                buttonText: _getButtonText(processState),
                                isDisabled: isPurchasing(),
                                primaryColor: primaryColor,
                                primaryLightColor: primaryLightColor,
                                onPrimaryColor: onPrimaryColor,
                              ),
                            );
                          },
                          childCount: items.length,
                        ),
                      ),
                    );
                  },
                  loading: () => const SliverFillRemaining(
                    child: Center(
                      child: CircularProgressIndicator(
                        color: Colors.white,
                      ),
                    ),
                  ),
                  error: (error, stack) => SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Failed to load subscription plans',
                            style: TextStyle(
                              color: Colors.red.shade300,
                              fontSize: 16.sp,
                            ),
                          ),
                          16.verticalSpace,
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              foregroundColor: onPrimaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.r),
                              ),
                            ),
                            onPressed: () {
                              ref.invalidate(subscriptionPurchaseProvider);
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // 底部信息
                SliverToBoxAdapter(
                  child: SafeArea(
                    child: Column(
                      children: [
                        20.verticalSpace,
                        AppButton(
                          type: AppButtonType.text,
                          text: 'Restore Purchases',
                          textStyle: (context, defaultStyle) =>
                              defaultStyle.copyWith(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            color: primaryLightColor,
                          ),
                          onPressed: () {
                            // 如果正在处理购买，禁止点击
                            if (isPurchasing()) {
                              LoadingUtils.showToast('Processing, please wait');
                              return;
                            }
                            ref
                                .read(subscriptionPurchaseProvider.notifier)
                                .restorePurchases();
                          },
                        ),
                        16.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 24.w),
                          child: Text(
                            'Payment will be charged to your Apple ID account at the confirmation of purchase. Subscription automatically renews unless it is canceled at least 24 hours before the end of the current period. Your account will be charged for renewal within 24 hours prior to the end of the current period. You can manage and cancel your subscriptions by going to your account settings on the App Store after purchase.',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Colors.white.withValues(alpha: 0.5),
                              height: 1.4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        24.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // 显示全屏加载指示器
            if (isPurchasing())
              Container(
                color: Colors.black.withValues(alpha: 0.7),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        color: primaryColor,
                      ),
                      16.verticalSpace,
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 20.w,
                          vertical: 10.h,
                        ),
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(30.r),
                        ),
                        child: Text(
                          processState == PurchaseProcessState.purchasing
                              ? 'Processing subscription...'
                              : 'Verifying subscription...',
                          style: TextStyle(
                            color: onPrimaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureRow(
      BuildContext context, String feature, Color textColor) {
    return Row(
      children: [
        Container(
          width: 24.r,
          height: 24.r,
          decoration: BoxDecoration(
            color: textColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(
            Icons.check,
            color: textColor,
            size: 16.r,
          ),
        ),
        12.horizontalSpace,
        Expanded(
          child: Text(
            feature,
            style: TextStyle(
              fontSize: 14.sp,
              color: textColor,
            ),
          ),
        ),
      ],
    );
  }

  String _getButtonText(PurchaseProcessState state) {
    switch (state) {
      case PurchaseProcessState.purchasing:
        return 'Processing...';
      case PurchaseProcessState.verifying:
        return 'Verifying...';
      default:
        return 'Subscribe';
    }
  }
}
