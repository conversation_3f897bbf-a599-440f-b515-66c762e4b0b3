# 网络请求自动重试机制实现

## 概述

本文档描述了为Flutter项目实现的网络请求自动重试机制。该机制基于`dio_smart_retry`库，提供了智能的重试策略，能够在网络不稳定的情况下自动重试失败的请求。

## 实现特性

### ✅ 已实现功能

1. **智能重试策略**
   - 默认策略：3次重试，指数退避（1秒、2秒、4秒）
   - 快速策略：2次重试，较短间隔（0.5秒、1秒）
   - 保守策略：5次重试，较长间隔（1、2、4、8、16秒）
   - 无重试策略：用于敏感操作

2. **自动重试条件**
   - 网络超时（连接超时、接收超时、发送超时）
   - 服务器错误（5xx状态码）
   - 网络连接问题
   - 429 Too Many Requests

3. **智能排除机制**
   - 不重试客户端错误（400、401、403、409、422）
   - 不重试业务逻辑错误（token过期、账户限制等）
   - 不重试用户取消的请求

4. **灵活配置**
   - 支持全局默认策略
   - 支持单个请求自定义策略
   - 支持完全禁用重试
   - 支持自定义重试次数和间隔

5. **日志记录**
   - 集成项目日志系统
   - 详细的重试过程记录
   - 便于调试和监控

## 文件结构

```
lib/shared/data/remote/
├── retry_config.dart              # 重试配置类和策略
├── retry_usage_example.dart       # 使用示例和最佳实践
├── dio_network_service.dart       # 已集成重试机制的网络服务
└── network_service.dart           # 网络服务抽象接口

test/shared/data/remote/
└── retry_config_test.dart          # 重试配置测试

docs/
└── network_retry_implementation.md # 本文档
```

## 核心组件

### 1. RetryStrategy 枚举

```dart
enum RetryStrategy {
  defaultStrategy,      // 默认策略：3次重试
  fastStrategy,         // 快速策略：2次重试
  conservativeStrategy, // 保守策略：5次重试
  noRetry,             // 无重试
}
```

### 2. RetryConfig 类

负责创建和管理重试配置，包括：
- 重试次数和延迟设置
- 自定义重试评估器
- 日志配置
- 拦截器创建

### 3. RetryConfigExtension 扩展

为`RequestOptions`提供便捷的重试策略设置方法：
- `setRetryStrategy(strategy)` - 设置重试策略
- `disableRetry()` - 禁用重试
- `retryStrategy` - 获取当前策略

## 使用方法

### 1. 默认使用（推荐）

所有通过`DioNetworkService`发送的请求都会自动应用默认重试策略：

```dart
// 无需额外配置，自动重试
final result = await networkService.get('/api/data');
```

### 2. 禁用重试（敏感操作）

```dart
final options = Options();
options.extra ??= {};
options.extra!['retryStrategy'] = RetryStrategy.noRetry;

await dio.post('/auth/login', 
  data: loginData, 
  options: options
);
```

### 3. 自定义重试策略

```dart
final options = Options();
options.extra ??= {};
options.extra!['retryStrategy'] = RetryStrategy.conservativeStrategy;

await dio.post('/api/important-sync', 
  data: syncData, 
  options: options
);
```

## 重试策略详情

| 策略 | 重试次数 | 重试间隔 | 适用场景 |
|------|----------|----------|----------|
| defaultStrategy | 3次 | 1s, 2s, 4s | 大多数API请求 |
| fastStrategy | 2次 | 0.5s, 1s | 实时性要求高的请求 |
| conservativeStrategy | 5次 | 1s, 2s, 4s, 8s, 16s | 重要操作、文件传输 |
| noRetry | 0次 | - | 登录、支付等敏感操作 |

## 最佳实践

### 1. 策略选择指南

- **默认策略**：适用于大多数API请求
- **快速策略**：适用于实时性要求高的请求（如聊天、实时数据）
- **保守策略**：适用于重要操作（如数据同步、文件上传）
- **无重试**：适用于敏感操作（如登录、支付、账户操作）

### 2. 错误处理

```dart
try {
  final result = await networkService.post('/api/data', data: requestData);
  // 处理成功响应
} catch (e) {
  // 重试失败后的错误处理
  if (e is AppException) {
    // 根据错误类型进行相应处理
    switch (e.statusCode) {
      case 401:
        // 跳转到登录页面
        break;
      case 500:
        // 显示服务器错误提示
        break;
      default:
        // 显示通用错误提示
    }
  }
}
```

### 3. 性能考虑

- 重试次数不宜过多（建议3-5次）
- 重试间隔要合理（避免长时间阻塞UI）
- 对于大文件传输，考虑使用断点续传
- 在网络状况良好时，大多数请求不会触发重试

## 监控和调试

### 1. 日志记录

重试过程会自动记录到项目日志系统：

```
[RetryInterceptor] Retrying request: POST /api/data (attempt 1/3)
[RetryInterceptor] Request retry successful: POST /api/data
```

### 2. 调试技巧

- 在开发环境启用详细日志
- 监控重试频率，识别网络问题
- 分析重试失败的原因，优化重试策略

## 测试

项目包含完整的单元测试，覆盖：
- 重试策略配置
- 拦截器创建
- 扩展方法功能
- 集成测试

运行测试：
```bash
flutter test test/shared/data/remote/retry_config_test.dart
```

## 依赖

- `dio: ^5.7.0` - HTTP客户端
- `dio_smart_retry: ^7.0.1` - 智能重试拦截器

## 总结

该重试机制实现了以下目标：

1. ✅ **提高应用稳定性**：自动处理临时网络问题
2. ✅ **智能重试策略**：避免不必要的重试，减少服务器压力
3. ✅ **灵活配置**：支持不同场景的重试需求
4. ✅ **易于使用**：最小化代码修改，自动生效
5. ✅ **完善监控**：详细的日志记录和错误处理

通过这个实现，应用在面对网络不稳定、服务器临时故障等情况时，能够自动恢复，显著提升用户体验。
